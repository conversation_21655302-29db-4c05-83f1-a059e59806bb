# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is QADChat, a modified version of NextChat (based on v2.16.0) with UI重构, logic重构, and removal of unused modules. The architecture has been重构 to use an Assistant-Topic-Message structure instead of Topic-Message.

## Common Development Commands

```bash
# Development
yarn dev                 # Start development server
yarn export:dev         # Development with export mode

# Building
yarn build              # Production build (standalone)
yarn export             # Static export build
yarn start              # Start production server

# Desktop App
yarn app:dev            # Tauri development
yarn app:build          # Build desktop app

# Testing & Quality
yarn test               # Run tests in watch mode
yarn test:ci            # CI test run
yarn lint               # ESLint check
yarn prompts            # Fetch latest prompts

# Utilities
yarn proxy-dev          # Development with proxy
```

## Architecture Overview

- **Framework**: Next.js 14.1.1 with App Router
- **State Management**: Zustand for global state
- **Styling**: SCSS modules with global theming
- **UI Library**: Ant Design components
- **Data Storage**: IndexedDB via idb-keyval, Local Storage
- **Testing**: Jest with jsdom environment

### Key Architectural Patterns

1. **App Router Structure**: Using Next.js 13+ App Router with dynamic routes
2. **Component-based**: Modular React components with co-located styles
3. **Store Pattern**: Zustand stores in `app/store/` for global state management
4. **API Routes**: Next.js API routes for backend functionality in `app/api/`
5. **Provider Pattern**: Multiple AI service providers with unified interface

### Directory Structure

- `app/`: Main application code
  - `api/`: API routes for different providers (openai, anthropic, etc.)
  - `components/`: React UI components with co-located styles
  - `store/`: Zustand stores for state management
  - `utils/`: Utility functions
  - `config/`: Configuration files
  - `mcp/`: Model Context Protocol integration
- `public/`: Static assets
- `docs/`: Documentation and guides
- `scripts/`: Build and utility scripts
- `src-tauri/`: Desktop app configuration (Rust/Tauri)

### Key Features

1. **Assistant-Topic-Message Architecture**: Topics are bound to assistants, each with independent topic lists
2. **Model Management**:重构 of model selection logic with capability icons
3. **MCP Integration**: Model Context Protocol servers with streamableHttp protocol
4. **Real-time Chat**: Audio streaming capabilities
5. **Internationalization**: Multi-language support (cn, en, tw)

### Development Notes

- Uses TypeScript 5.2.2 with strict mode enabled
- ESLint and Prettier for code formatting
- Husky for git hooks
- SVG files automatically converted to React components via SVGR
- Custom webpack configuration for SVG handling and chunk optimization
- Environment-based build modes (standalone, export)